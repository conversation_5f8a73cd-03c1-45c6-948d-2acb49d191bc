import React, { useState, useEffect } from 'react';
import { Upload, message, Radio, Button, Card } from 'antd';
import { DeleteOutlined, FileOutlined } from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';
import { api } from '@/services';
import type { MediaFile, MediaType } from '@/types/api';

interface UploadedFile {
  uid: string;
  name: string;
  url: string;
  type?: number;
}

interface MusicUploadProps {
  /** 上传成功回调，返回包含类型信息的媒体数组 */
  onUploadSuccess?: (medias: MediaFile[]) => void;
  /** 初始媒体文件列表（用于回显） */
  initialMedias?: MediaFile[];
}

const MusicUpload: React.FC<MusicUploadProps> = ({
  onUploadSuccess,
  initialMedias = [],
}) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [mediaTypes, setMediaTypes] = useState<MediaType[]>([]);

  // 获取媒体类型列表
  useEffect(() => {
    const fetchMediaTypes = async () => {
      try {
        const response = await api.music.getTrackMediaTypes();
        if (response.code === 200) {
          setMediaTypes(response.body);
        }
      } catch (error) {
        console.error('获取媒体类型失败:', error);
        // 设置默认媒体类型
        setMediaTypes([
          { type: 1, name: 'Primary Music Track' },
          { type: 2, name: 'Primary Music Video' },
          { type: 3, name: 'Track Sample' },
          { type: 4, name: 'Music Video Sample' },
        ]);
      }
    };
    fetchMediaTypes();
  }, []);

  // 初始化已上传文件
  useEffect(() => {
    if (initialMedias.length > 0) {
      const files = initialMedias.map((media, index) => ({
        uid: `initial-${index}`,
        name: `Media ${index + 1}`,
        url: media.mediaUrl,
        type: media.type,
      }));
      setUploadedFiles(files);
    }
  }, [initialMedias]);

  // 媒体文件类型验证
  const beforeUpload = (file: File) => {
    const isAudio = file.type.startsWith('audio/');
    const isVideo = file.type.startsWith('video/');

    if (!isAudio && !isVideo) {
      message.error(t('messages.uploadMediaFailed'));
      return false;
    }

    // 检查文件大小 (限制为500MB)
    const isLt500M = file.size / 1024 / 1024 < 500;
    if (!isLt500M) {
      message.error(t('messages.uploadMediaSizeFailed'));
      return false;
    }

    return true;
  };

  // 处理文件上传
  const handleUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setLoading(true);

    try {
      // 根据文件类型选择合适的上传方法
      let response: { url: string };
      if (file.type.startsWith('audio/')) {
        response = await api.user.uploadAudio(file);
      } else if (file.type.startsWith('video/')) {
        response = await api.user.uploadVideo(file);
      } else {
        throw new Error('不支持的文件类型');
      }

      // 添加到已上传文件列表，不设置默认类型，用户必须主动选择
      const newFile: UploadedFile = {
        uid: file.uid,
        name: file.name,
        url: response.url,
        type: undefined, // 用户必须主动选择类型
      };

      setUploadedFiles(prev => [...prev, newFile]);
      onSuccess({ url: response.url });
      setLoading(false);
      message.success(t('messages.uploadSuccess'));

      // 触发回调
      updateMedias([...uploadedFiles, newFile]);
    } catch (error) {
      onError(error);
      setLoading(false);
      message.error(t('messages.uploadFailed'));
    }
  };

  // 更新文件类型
  const handleTypeChange = (uid: string, type: number) => {
    const updatedFiles = uploadedFiles.map(file =>
      file.uid === uid ? { ...file, type } : file
    );
    setUploadedFiles(updatedFiles);
    updateMedias(updatedFiles);
  };

  // 删除文件
  const handleDelete = (uid: string) => {
    const updatedFiles = uploadedFiles.filter(file => file.uid !== uid);
    setUploadedFiles(updatedFiles);
    updateMedias(updatedFiles);
  };

  // 更新媒体数组并触发回调
  const updateMedias = (files: UploadedFile[]) => {
    const medias: MediaFile[] = files
      .filter(file => file.type !== undefined)
      .map(file => ({
        mediaUrl: file.url,
        type: file.type!,
      }));
    onUploadSuccess?.(medias);
  };

  return (
    <div className="space-y-4">
      {/* 上传区域 */}
      <Upload
        name="mediaFiles"
        multiple
        accept="audio/*,video/*"
        beforeUpload={beforeUpload}
        customRequest={handleUpload}
        showUploadList={false}
        className="music-uploader"
        disabled={loading}
      >
        <div className="w-[218px] h-[218px] bg-[#282828] flex flex-col items-center justify-center cursor-pointer hover:bg-[#333333] transition-colors">
          <div className="w-16 h-16 relative mb-4">
            {loading ? (
              <div className="w-16 h-16 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff5e13]"></div>
              </div>
            ) : (
              <>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-16 h-1 bg-[#999999] rounded-[11px]"></div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center rotate-90">
                  <div className="w-16 h-1 bg-[#999999] rounded-[11px]"></div>
                </div>
              </>
            )}
          </div>
          <div className="text-label text-[14px] font-medium">
            {loading ? t('messages.uploading') : t('submitMusic.uploadMedia')}
          </div>
        </div>
      </Upload>

      {/* 已上传文件列表 */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white text-sm font-medium">
            {t('submitMusic.mediaTypes.uploadedFiles')}：
          </h4>
          {uploadedFiles.map(file => (
            <Card
              key={file.uid}
              size="small"
              className="bg-[#1a1a1a] border-[#333]"
              styles={{ body: { padding: '12px' } }}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <FileOutlined className="text-[#ff5e13]" />
                    <span className="text-white text-sm truncate max-w-[200px]">
                      {file.name}
                    </span>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => handleDelete(file.uid)}
                    className="text-red-500 hover:text-red-400"
                  />
                </div>
                <div className="space-y-2">
                  {!file.type && (
                    <span className="text-yellow-500 text-xs">
                      {t('submitMusic.mediaTypes.selectMediaType')}
                    </span>
                  )}
                  <Radio.Group
                    value={file.type}
                    onChange={e => handleTypeChange(file.uid, e.target.value)}
                    size="small"
                    className="flex flex-wrap gap-2"
                  >
                    {mediaTypes.map(mediaType => (
                      <Radio key={mediaType.type} value={mediaType.type}>
                        <span className="text-white text-xs">
                          {mediaType.name}
                        </span>
                      </Radio>
                    ))}
                  </Radio.Group>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default MusicUpload;
