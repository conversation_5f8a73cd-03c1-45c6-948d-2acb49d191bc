/**
 * 批量上传音乐作品脚本
 * 使用方法：
 * 1. 修改 music-data-template.json 文件，添加你的音乐信息和封面URL
 * 2. 获取登录token并配置到 CONFIG.AUTH_TOKEN
 * 3. 在项目根目录运行：node scripts/batch-upload-music.js
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const AUTH_TOKENS = [
  `eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJkMTk1NzFkMC02NzU3LTRkZGUtYmU1Zi0zYTI0ZmQ2NTgxNWQiLCJpYXQiOjE3NTY5ODM0MTIsImV4cCI6MTc1Njk4NzAxMiwianRpIjoiNGU4MjMwOTUtNDdlNi00OWJjLWFkOGQtZDZkMWU4MTk2N2UwIn0.CoKFJMeEyBYLH6_gwzaJoey-tuAxXF1zrvERtGOYjRY`,
  `eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJiZDVjZGJjNC00MzdkLTQ0ZTItOTU5Mi0wMmI5OTJmNTlhZmQiLCJpYXQiOjE3NTY5ODMxNDIsImV4cCI6MTc1Njk4Njc0MiwianRpIjoiYmY1N2MwZGEtNDYyZi00ZWIxLTkwNzMtOTBhZDUyY2EyYjU5In0.YEqcHvr3Agw-ss_fZaocSYpDc69IZ4UQh1trcATkHWU`,
  `eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJlYjI0YmJiNC1iNTBhLTRkYTgtOWFkNi1lNDNlMGNmNDE0ZDgiLCJpYXQiOjE3NTY5ODM0MzMsImV4cCI6MTc1Njk4NzAzMywianRpIjoiMzRmM2Y5YmEtNGIxMy00OTE0LTg0MjQtYzQwNDcxOTg0N2U0In0.dN4dHaYQbzd_0dWHVqV7ausqT7yrAFALgyEyTsFxLBE`,
];
// 配置信息
const CONFIG = {
  // API 基础URL - 根据你的环境修改
  BASE_URL: 'https://musicapi.renee-arts.com/api/v1', // 开发环境

  // 请求间隔（毫秒）- 避免请求过于频繁
  REQUEST_INTERVAL: 1000,

  // 数据文件路径 - 使用生成的50条数据
  DATA_FILE: path.join(__dirname, 'music-data-130-tracks.json'),
};

// 从JSON文件加载数据
function loadMusicData() {
  try {
    if (!fs.existsSync(CONFIG.DATA_FILE)) {
      console.log(`❌ 数据文件不存在: ${CONFIG.DATA_FILE}`);
      console.log('💡 请先创建 music-data-template.json 文件');
      return null;
    }

    const rawData = fs.readFileSync(CONFIG.DATA_FILE, 'utf8');
    const data = JSON.parse(rawData);

    if (!data.musicData || !Array.isArray(data.musicData)) {
      console.log('❌ 数据文件格式错误: 缺少 musicData 数组');
      return null;
    }

    if (!data.coverUrls || !Array.isArray(data.coverUrls)) {
      console.log('❌ 数据文件格式错误: 缺少 coverUrls 数组');
      return null;
    }

    console.log(`✅ 成功加载 ${data.musicData.length} 条音乐数据`);
    console.log(`✅ 成功加载 ${data.coverUrls.length} 个封面URL`);

    return data;
  } catch (error) {
    console.log(`❌ 加载数据文件失败: ${error.message}`);
    return null;
  }
}

// 创建HTTP客户端的工厂函数
function createApiClient(authToken) {
  return axios.create({
    baseURL: CONFIG.BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${authToken}`,
      'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
      'X-Realm': 1,
      'Accept-Language': 'zh',
    },
  });
}

// 生成curl命令格式的请求
function generateCurlCommand(url, data, headers) {
  const curlHeaders = Object.entries(headers)
    .map(([key, value]) => `-H "${key}: ${value}"`)
    .join(' ');

  const curlData = JSON.stringify(data, null, 2).replace(/"/g, '\\"');

  return `curl -X POST "${url}" \\
${curlHeaders} \\
-d "${curlData}"`;
}

// 保存失败的请求到文件
function saveFailedRequest(
  trackData,
  coverUrl,
  error,
  response = null,
  authToken
) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `failed-request-${trackData.title.replace(/[^a-zA-Z0-9]/g, '_')}-${timestamp}.txt`;
  const filepath = path.join(__dirname, 'failed-requests', filename);

  // 确保目录存在
  const dir = path.dirname(filepath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const submitData = {
    ...trackData,
    coverArtUrl: coverUrl,
  };

  const fullUrl = `${CONFIG.BASE_URL}/member/track`;
  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${authToken}`,
    'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'X-Realm': '1',
    'Accept-Language': 'zh',
  };

  const curlCommand = generateCurlCommand(fullUrl, submitData, headers);

  const debugInfo = `
=== 失败请求调试信息 ===
时间: ${new Date().toISOString()}
歌曲: ${trackData.title}
错误: ${error}

=== 响应信息 ===
${response ? JSON.stringify(response.data, null, 2) : '无响应数据'}

=== HTTP状态码 ===
${response ? response.status : '无状态码'}

=== 请求数据 ===
${JSON.stringify(submitData, null, 2)}

=== CURL命令 (可直接复制使用) ===
${curlCommand}

=== 请求头 ===
${JSON.stringify(headers, null, 2)}
`;

  try {
    fs.writeFileSync(filepath, debugInfo);
    console.log(`🐛 调试信息已保存到: ${filepath}`);
  } catch (saveError) {
    console.log(`❌ 保存调试信息失败: ${saveError.message}`);
  }
}

// 提交单个音乐作品
async function submitTrack(trackData, coverUrl, apiClient, authToken) {
  try {
    const submitData = {
      ...trackData,
      coverArtUrl: coverUrl,
    };

    console.log(`正在提交: ${trackData.title}`);

    const response = await apiClient.post('/member/track', submitData);

    if (response.data.code === 200) {
      console.log(
        `✅ 成功提交: ${trackData.title}, ID: ${response.data.body.id}`
      );
      return {
        success: true,
        id: response.data.body.id,
        title: trackData.title,
      };
    } else {
      console.log(
        `❌ 提交失败: ${trackData.title}, 错误: ${response.data.message}`
      );

      // 保存失败请求的调试信息
      saveFailedRequest(
        trackData,
        coverUrl,
        response.data.message,
        response,
        authToken
      );

      return {
        success: false,
        error: response.data.message,
        title: trackData.title,
        debugInfo: `详细信息已保存到 failed-requests/ 目录`,
      };
    }
  } catch (error) {
    console.log(`❌ 提交失败: ${trackData.title}, 错误: ${error.message}`);

    // 保存失败请求的调试信息
    saveFailedRequest(
      trackData,
      coverUrl,
      error.message,
      error.response,
      authToken
    );

    return {
      success: false,
      error: error.message,
      title: trackData.title,
      debugInfo: `详细信息已保存到 failed-requests/ 目录`,
    };
  }
}

// 数据分配函数 - 将数据平均分配给每个token
function distributeDataToTokens(musicData, coverUrls, authTokens) {
  const tokenCount = authTokens.length;
  const itemsPerToken = Math.ceil(musicData.length / tokenCount);

  console.log(`📊 数据分配策略:`);
  console.log(`  - Token数量: ${tokenCount}`);
  console.log(`  - 总数据量: ${musicData.length}`);
  console.log(`  - 每个token分配: ${itemsPerToken} 条数据`);

  const distribution = [];

  for (let i = 0; i < tokenCount; i++) {
    const startIndex = i * itemsPerToken;
    const endIndex = Math.min(startIndex + itemsPerToken, musicData.length);

    if (startIndex < musicData.length) {
      const tokenData = {
        token: authTokens[i],
        musicData: musicData.slice(startIndex, endIndex),
        coverUrls: coverUrls.slice(startIndex, endIndex),
        startIndex,
        endIndex: endIndex - 1,
        count: endIndex - startIndex,
      };

      distribution.push(tokenData);
      console.log(
        `  - Token ${i + 1}: 处理数据 ${startIndex + 1}-${endIndex} (${tokenData.count}条)`
      );
    }
  }

  return distribution;
}

// 批量上传主函数
async function batchUpload(musicData, coverUrls) {
  console.log('🚀 开始批量上传音乐作品...');
  console.log(`📊 总计: ${musicData.length} 首歌曲`);

  // 检查AUTH_TOKENS是否有效
  if (!AUTH_TOKENS || AUTH_TOKENS.length === 0) {
    console.log('❌ AUTH_TOKENS 为空，请先配置token');
    return [];
  }

  // 分配数据给每个token
  const distribution = distributeDataToTokens(
    musicData,
    coverUrls,
    AUTH_TOKENS
  );

  console.log('\n🔄 开始按token串行处理...');

  const allResults = [];
  let totalProcessed = 0;

  // 按token串行处理每个数据块
  for (let tokenIndex = 0; tokenIndex < distribution.length; tokenIndex++) {
    const tokenData = distribution[tokenIndex];
    const apiClient = createApiClient(tokenData.token);

    console.log(
      `\n🎯 使用Token ${tokenIndex + 1}/${distribution.length} 处理数据...`
    );
    console.log(
      `📝 处理范围: ${tokenData.startIndex + 1}-${tokenData.endIndex + 1} (${tokenData.count}条)`
    );

    // 处理当前token的数据
    for (let i = 0; i < tokenData.musicData.length; i++) {
      const trackData = tokenData.musicData[i];
      const coverUrl = tokenData.coverUrls[i];
      const globalIndex = tokenData.startIndex + i + 1;

      console.log(
        `\n📝 [${globalIndex}/${musicData.length}] [Token${tokenIndex + 1}] 处理中...`
      );

      // 添加时间戳字段
      const trackDataWithTimestamps = {
        ...trackData,
        originalReleaseDate: Date.now(),
        streetDate: Date.now(),
      };

      const result = await submitTrack(
        trackDataWithTimestamps,
        coverUrl,
        apiClient,
        tokenData.token
      );
      result.tokenIndex = tokenIndex + 1; // 添加token标识
      allResults.push(result);
      totalProcessed++;

      // 请求间隔
      if (totalProcessed < musicData.length) {
        console.log(`⏳ 等待 ${CONFIG.REQUEST_INTERVAL}ms...`);
        await new Promise(resolve =>
          setTimeout(resolve, CONFIG.REQUEST_INTERVAL)
        );
      }
    }

    console.log(
      `✅ Token ${tokenIndex + 1} 处理完成，已处理 ${tokenData.count} 条数据`
    );
  }

  // 输出统计结果
  const successCount = allResults.filter(r => r.success).length;
  const failCount = allResults.filter(r => !r.success).length;

  console.log('\n📊 上传完成统计:');
  console.log(`✅ 成功: ${successCount} 首`);
  console.log(`❌ 失败: ${failCount} 首`);

  // 按token分组统计
  console.log('\n📈 各Token处理统计:');
  for (let i = 0; i < AUTH_TOKENS.length; i++) {
    const tokenResults = allResults.filter(r => r.tokenIndex === i + 1);
    const tokenSuccess = tokenResults.filter(r => r.success).length;
    const tokenFail = tokenResults.filter(r => !r.success).length;
    console.log(
      `  Token ${i + 1}: 成功 ${tokenSuccess}, 失败 ${tokenFail}, 总计 ${tokenResults.length}`
    );
  }

  if (failCount > 0) {
    console.log('\n❌ 失败列表:');
    allResults
      .filter(r => !r.success)
      .forEach(r => {
        console.log(`  - [Token${r.tokenIndex}] ${r.title}: ${r.error}`);
        if (r.debugInfo) {
          console.log(`    💡 ${r.debugInfo}`);
        }
      });

    console.log('\n🐛 调试提示:');
    console.log('  - 失败请求的详细信息已保存到 scripts/failed-requests/ 目录');
    console.log('  - 每个失败请求都包含完整的curl命令，可直接复制测试');
    console.log('  - 检查token是否过期、请求数据格式是否正确');
  }

  if (successCount > 0) {
    console.log('\n✅ 成功列表:');
    allResults
      .filter(r => r.success)
      .forEach(r => {
        console.log(`  - [Token${r.tokenIndex}] ${r.title} (ID: ${r.id})`);
      });
  }

  return allResults;
}

// 获取元数据信息的辅助函数
async function getMetadata() {
  try {
    console.log('📋 获取元数据信息...');

    // 使用第一个token获取元数据
    const apiClient = createApiClient(AUTH_TOKENS[0]);

    const [genresRes, audioFormatsRes, releaseOptionsRes] = await Promise.all([
      apiClient.get('/meta/default/genres'),
      apiClient.get('/meta/default/audio-formats'),
      apiClient.get('/meta/default/release-options'),
    ]);

    console.log('\n🎵 可用的音乐类型:');
    genresRes.data.body.forEach(genre => {
      console.log(`  - ${genre.code}: ${genre.name}`);
    });

    console.log('\n🎧 可用的音频格式:');
    audioFormatsRes.data.body.forEach(format => {
      console.log(`  - ${format.code}: ${format.name}`);
    });

    console.log('\n📀 可用的发布选项:');
    releaseOptionsRes.data.body.forEach(option => {
      console.log(`  - ${option.code}: ${option.name}`);
    });

    // 返回元数据供其他函数使用
    return {
      genres: genresRes.data.body,
      audioFormats: audioFormatsRes.data.body,
      releaseOptions: releaseOptionsRes.data.body,
    };
  } catch (error) {
    console.log('❌ 获取元数据失败:', error.message);
    return null;
  }
}

// 主程序
async function main() {
  console.log('🎵 音乐作品批量上传工具');
  console.log('================================');

  // 检查配置
  if (!AUTH_TOKENS || AUTH_TOKENS.length === 0) {
    console.log('❌ 请先配置 AUTH_TOKENS 数组');
    console.log(
      '💡 提示: 先登录系统获取token，然后修改脚本中的 AUTH_TOKENS 数组'
    );
    return;
  }

  console.log(`🔑 已配置 ${AUTH_TOKENS.length} 个认证token`);
  AUTH_TOKENS.forEach((token, index) => {
    const shortToken = token.substring(0, 20) + '...';
    console.log(`  Token ${index + 1}: ${shortToken}`);
  });

  // 加载音乐数据
  const data = loadMusicData();

  if (!data) {
    console.log('❌ 无法加载音乐数据，程序退出');
    return;
  }

  const { musicData, coverUrls } = data;

  if (musicData.length === 0) {
    console.log('❌ 音乐数据为空，请先添加数据到 music-data-template.json');
    return;
  }

  // 获取元数据信息（可选）
  await getMetadata();

  console.log('\n⚠️  请确认以下信息:');
  console.log(`📊 准备上传 ${musicData.length} 首歌曲`);
  console.log(`🖼️  可用封面 ${coverUrls.length} 个`);
  console.log(`🔑 使用Token数量: ${AUTH_TOKENS.length} 个`);
  console.log(`🌐 API地址: ${CONFIG.BASE_URL}`);
  console.log(`⏱️  请求间隔: ${CONFIG.REQUEST_INTERVAL}ms`);

  // 开始批量上传
  const results = await batchUpload(musicData.slice(3), coverUrls.slice(3));

  console.log('\n🎉 批量上传完成!');

  // 保存结果到文件
  const resultFile = path.join(__dirname, `upload-results-${Date.now()}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  console.log(`📄 结果已保存到: ${resultFile}`);
}

// 运行主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { submitTrack, batchUpload, getMetadata };
