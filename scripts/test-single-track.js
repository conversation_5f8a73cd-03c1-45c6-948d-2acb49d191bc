/**
 * 测试单个音乐作品提交的脚本
 * 用于验证数据格式修复是否有效
 * 运行：node scripts/test-single-track.js
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置信息
const CONFIG = {
  BASE_URL: 'https://musicapi.renee-arts.com/api/v1',
  // 使用成功案例的token
  AUTH_TOKEN: `eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJiZDVjZGJjNC00MzdkLTQ0ZTItOTU5Mi0wMmI5OTJmNTlhZmQiLCJpYXQiOjE3NTY5MTc5MTksImV4cCI6MTc1NjkyMTUxOSwianRpIjoiYjQ4NzczYmUtZjcyMS00ZmEzLThiNTQtMmUxZWYzNjBiNjhmIn0.3Q3Dg0-TCvX4L6mX2oiwQdWj1TaTnKXtwM3esHpPNQk`,
};

// 创建HTTP客户端
const apiClient = axios.create({
  baseURL: CONFIG.BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${CONFIG.AUTH_TOKEN}`,
    'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'X-Realm': 1,
    'Accept-Language': 'zh',
  },
});

// 生成curl命令格式的请求
function generateCurlCommand(url, data, headers) {
  const curlHeaders = Object.entries(headers)
    .map(([key, value]) => `-H "${key}: ${value}"`)
    .join(' ');

  const curlData = JSON.stringify(data, null, 2).replace(/"/g, '\\"');

  return `curl -X POST "${url}" \\
${curlHeaders} \\
-d "${curlData}"`;
}

// 测试数据（使用成功案例的格式）
const testTrackData = {
  title: 'Test Track - Fixed Format',
  labelName: 'Test Records',
  albumName: 'Test Album',
  trackInfo: 'A test track to verify the fixed data format',
  primaryLanguage: 'en',
  upc: '123456789999',
  isrc: 'TEST24000999',
  primaryGenreId: 'music.genre.pop',
  secondaryGenreId: 'music.genre.electronic',
  originalReleaseDate: Date.now(),
  streetDate: Date.now(),
  copyrightName: 'Test Copyright',
  copyrightYear: 2024,
  phonogramCopyright: 'Test Phonogram',
  phonogramCopyrightYear: 2024,
  coverArtUrl:
    'https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/thewitch_thewizard-bones',
  // 使用正确的API格式
  audioFormats: [
    'music.audio-format.supports-dolby-atmos',
    'music.audio-format.apple-digital-masters',
  ],
  releaseOptions: [
    'music.release-option.enable-itunes-pre-order',
    'music.release-option.live-concert-recording',
  ],
  medias: [
    {
      mediaUrl: 'https://cdn.example.com/audio/shining-lights.mp3',
      type: 1,
    },
    {
      mediaUrl: 'https://cdn.example.com/audio/shining-lights.flac',
      type: 3,
    },
  ],
};

// 测试提交
async function testSubmit() {
  console.log('🧪 测试单个音乐作品提交...');
  console.log(`🎵 歌曲: ${testTrackData.title}`);

  try {
    console.log('\n📤 正在提交...');
    const response = await apiClient.post('/member/track', testTrackData);

    if (response.data.code === 200) {
      console.log('✅ 提交成功!');
      console.log(`🆔 返回ID: ${response.data.body.id}`);
      console.log(`📊 响应数据:`, JSON.stringify(response.data, null, 2));
    } else {
      console.log('❌ 提交失败');
      console.log(`错误信息: ${response.data.message}`);
      console.log(`响应数据:`, JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ 请求失败');
    console.log(`错误: ${error.message}`);

    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`响应数据:`, JSON.stringify(error.response.data, null, 2));
    }

    // 生成curl命令用于调试
    const fullUrl = `${CONFIG.BASE_URL}/member/track`;
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${CONFIG.AUTH_TOKEN}`,
      'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
      'X-Realm': '1',
      'Accept-Language': 'zh',
    };

    const curlCommand = generateCurlCommand(fullUrl, testTrackData, headers);

    console.log('\n🐛 调试用curl命令:');
    console.log(curlCommand);

    // 保存到文件
    const debugFile = path.join(__dirname, 'test-debug.txt');
    const debugInfo = `
=== 测试失败调试信息 ===
时间: ${new Date().toISOString()}
错误: ${error.message}
状态码: ${error.response?.status || '无'}

=== 响应数据 ===
${error.response ? JSON.stringify(error.response.data, null, 2) : '无响应数据'}

=== 请求数据 ===
${JSON.stringify(testTrackData, null, 2)}

=== CURL命令 ===
${curlCommand}
`;

    fs.writeFileSync(debugFile, debugInfo);
    console.log(`📄 调试信息已保存到: ${debugFile}`);
  }
}

// 主函数
async function main() {
  console.log('🧪 单个音乐作品提交测试');
  console.log('========================');

  await testSubmit();

  console.log('\n🎉 测试完成!');
}

// 运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testSubmit };
