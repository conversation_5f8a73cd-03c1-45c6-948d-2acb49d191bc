{"coverUrls": ["https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/no_rush-tokyo_tea_room", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/hurry_up_tomorrow-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/demo-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/everythingisworseatnight-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_boy_is_mine-a<PERSON>a_grande", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/clancy-twenty_one_pilots", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/we_don_t_trust_you-future", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/hit_me_hard_and_soft-billie_eilish", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/radical_optimism-dua_lipa", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/moon_music-coldplay", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/eternal_sunshine-ariana_grande", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/mixtape_pluto-future", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/gnx-kend<PERSON>_lamar", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/softwareupdate2.0-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/cadaver-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/zip-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/champion-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_tortured_poets_department_the_anthology-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/for_all_the_dogs-drake", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/one_thing_at_a_time-morgan_wallen", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/espresso-sabrina_carpenter", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/guts-olivia_rod<PERSON>o", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/starboy_deluxe_-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/1989_taylor_s_version_-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/la_vida_es_una-myke_towers", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/coco_moon-owl_city", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/dead_club_city_extended_deluxe_-nothing_but_thieves", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/sos_deluxe_-sza", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/love_sick_deluxe_-don_toliver", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/songs_of_surrender-u2", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/speak_now_taylor_s_version_-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/dead_club_city-nothing_but_thieves", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/spider-man_across_the_spider-verse-metro_boomin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/manana_sera_bonito-karol_g", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/5-ed_sheeran", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/unreal_unearth-hozier", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/austin-post_malone", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/did_you_know_that_there_s_a_tunnel_under_ocean_blvd-lana_del_rey", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/utopia-travis_scott", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/autumn_variations-ed_sheeran", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/love_sick-don_toliver", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/amar-bigxthaplug", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/cuts_bruises-inhaler", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/a_dream_about_love-circa_survive", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/fantasy-m83", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/nadie_sabe_lo_que_va_a_pasar_manana-bad_bunny", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/eternal_embers-meltt", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/coping_habits-beach_vacation", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/exit_signs-leap", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/thewitch_thewizard-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/sos-sza", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/ctrl_deluxe_-sza", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/scenic_drive_the_tape_-khalid", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/montero-lil_nas_x", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/positions-ariana_grande", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/daystar-tory_lanez", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/nectar-joji", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/free_spirit-khalid", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/chixtape_5-tory_lanez", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/love_me_now_-tory_lanez", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/memories_don_t_die-tory_lanez", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/ballads_1-joji", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/more_life-drake", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/american_teen-khalid", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/endless-frank_ocean", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/starboy-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/beauty_behind_the_madness-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/kiss_land-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/s-sza", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/channel_orange-frank_ocean", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/house_of_balloons-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/just_whitney-whitney_houston", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/my_love_is_your_love-whitney_houston", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_velvet_rope-janet_jackson", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/london_calling-the_clash", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/never_mind_the_bollocks-sex_pistols", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_clash-the_clash", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/ramones-ramones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/ellington_at_newport-duke_ellington", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/kind_of_blue-miles_davis", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/mingus_ah_um-charles_mingus", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/live_at_the_apollo-james_brown", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/please_please_me-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/with_the_beatles-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/a_hard_day_s_night-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/highway_61_revisited-bob_dylan", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/help_-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/rubber_soul-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/a_love_supreme-john_coltrane", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/blonde_on_blonde-bob_dylan", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/pet_sounds-the_beach_boys", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/revolver-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/are_you_experienced-ji<PERSON>_hendrix", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/i_never_loved_a_man_the_way_i_love_you-aretha_franklin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/strange_days-the_doors", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/disraeli_gears-cream", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/axis_bold_as_love-the_jimi_hendrix_experience", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_doors-the_doors", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/sgt._pepper_s_lonely_hearts_club_band-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/wheels_of_fire-cream", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/truth-jeff_beck", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/astral_weeks-van_morrison", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/electric_ladyland-jimi_hendrix", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_white_album-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_beatles_the_white_album_-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/beggars_banquet-the_rolling_stones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/electric_ladyland-the_jimi_hendrix_experience", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/tommy-the_who", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/let_it_bleed-the_rolling_stones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/led_zeppelin_ii-led_zeppelin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/led_zeppelin-led_zeppelin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/abbey_road-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/paranoid-black_sabbath", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/fun_house-the_stooges", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/bitches_brew-miles_davis", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/in_rock-deep_purple", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/let_it_be-the_beatles", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/forca_bruta-jorge_ben", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/master_of_reality-black_sabbath", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/el_derecho_de_vivir_en_paz-victor_jara", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/electric_warrior-t._rex", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/who_s_next-the_who", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/sticky_fingers-the_rolling_stones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/led_zeppelin_iv-led_zeppelin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/what_s_going_on-marvin_gaye", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_rise_and_fall_of_ziggy_stardust-david_bowie", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/got_to_be_there-micha<PERSON>_jackson", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/transformer-lou_reed"], "musicData": [{"title": "Midnight Dreams", "labelName": "Starlight Records", "albumName": "Neon Nights", "trackInfo": "An electrifying pop anthem about chasing dreams under city lights", "primaryLanguage": "en", "upc": "123456780001", "isrc": "USRC25001", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Starlight Records", "copyrightYear": "2024", "phonogramCopyright": "Starlight Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Golden Hour", "labelName": "Sunset Music", "albumName": "Summer Vibes", "trackInfo": "A feel-good pop track celebrating life's perfect moments", "primaryLanguage": "en", "upc": "123456780002", "isrc": "USRC25002", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Sunset Music", "copyrightYear": "2024", "phonogramCopyright": "Sunset Music", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Love", "labelName": "Future Pop Records", "albumName": "Digital Hearts", "trackInfo": "A synth-pop masterpiece about modern romance in the digital age", "primaryLanguage": "en", "upc": "123456780003", "isrc": "USRC25003", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.synth-pop", "copyrightName": "Future Pop Records", "copyrightYear": "2024", "phonogramCopyright": "Future Pop Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Butterfly Effect", "labelName": "Indie Pop Collective", "albumName": "Small Changes", "trackInfo": "An introspective pop ballad about how small moments can change everything", "primaryLanguage": "en", "upc": "123456780004", "isrc": "USRC25004", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.indie-pop", "copyrightName": "Indie Pop Collective", "copyrightYear": "2024", "phonogramCopyright": "Indie Pop Collective", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Lights", "labelName": "City Sounds", "albumName": "Urban Nights", "trackInfo": "A vibrant pop anthem celebrating nightlife and city energy", "primaryLanguage": "en", "upc": "123456780005", "isrc": "USRC25005", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "City Sounds", "copyrightYear": "2024", "phonogramCopyright": "City Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Gravity", "labelName": "Emotional Records", "albumName": "Weightless", "trackInfo": "A powerful pop ballad about feeling pulled toward someone special", "primaryLanguage": "en", "upc": "123456780006", "isrc": "USRC25006", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rock", "copyrightName": "Emotional Records", "copyrightYear": "2024", "phonogramCopyright": "Emotional Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Phoenix Rising", "labelName": "Empowerment Music", "albumName": "Rebirth", "trackInfo": "An uplifting pop anthem about overcoming challenges and rising stronger", "primaryLanguage": "en", "upc": "123456780007", "isrc": "USRC25007", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Empowerment Music", "copyrightYear": "2024", "phonogramCopyright": "Empowerment Music", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Ocean Waves", "labelName": "Coastal Records", "albumName": "Tidal Emotions", "trackInfo": "A dreamy pop song using ocean metaphors to describe deep emotions", "primaryLanguage": "en", "upc": "123456780008", "isrc": "USRC25008", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.indie", "copyrightName": "Coastal Records", "copyrightYear": "2024", "phonogramCopyright": "Coastal Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Stardust", "labelName": "Cosmic Pop", "albumName": "Celestial Dreams", "trackInfo": "A cosmic pop journey about finding magic in everyday moments", "primaryLanguage": "en", "upc": "123456780009", "isrc": "USRC25009", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Cosmic Pop", "copyrightYear": "2024", "phonogramCopyright": "Cosmic Pop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Mirror Ball", "labelName": "Disco Revival", "albumName": "Retro Future", "trackInfo": "A disco-influenced pop track that brings back the glamour of the dance floor", "primaryLanguage": "en", "upc": "123456780010", "isrc": "USRC25010", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.disco", "copyrightName": "Disco Revival", "copyrightYear": "2024", "phonogramCopyright": "Disco Revival", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Velvet Dreams", "labelName": "Smooth Sounds", "albumName": "Midnight Velvet", "trackInfo": "A sultry pop ballad with smooth vocals and dreamy production", "primaryLanguage": "en", "upc": "123456780011", "isrc": "USRC25011", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Smooth Sounds", "copyrightYear": "2024", "phonogramCopyright": "Smooth Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Wildfire", "labelName": "Passion Records", "albumName": "Burning Bright", "trackInfo": "An intense pop-rock anthem about unstoppable passion and desire", "primaryLanguage": "en", "upc": "123456780012", "isrc": "USRC25012", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rock", "copyrightName": "Passion Records", "copyrightYear": "2024", "phonogramCopyright": "Passion Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Crystal Clear", "labelName": "Pure Pop", "albumName": "Transparent", "trackInfo": "A pristine pop track about clarity and honest communication in relationships", "primaryLanguage": "en", "upc": "123456780013", "isrc": "USRC25013", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.indie-pop", "copyrightName": "Pure Pop", "copyrightYear": "2024", "phonogramCopyright": "Pure Pop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Paradise", "labelName": "Electric Dreams", "albumName": "Synthetic Love", "trackInfo": "A vibrant synthpop celebration of artificial beauty and digital romance", "primaryLanguage": "en", "upc": "123456780014", "isrc": "USRC25014", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.synth-pop", "copyrightName": "Electric Dreams", "copyrightYear": "2024", "phonogramCopyright": "Electric Dreams", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Shooting Stars", "labelName": "Wish Records", "albumName": "Make a Wish", "trackInfo": "An uplifting pop anthem about following your dreams and making wishes come true", "primaryLanguage": "en", "upc": "123456780015", "isrc": "USRC25015", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Wish Records", "copyrightYear": "2024", "phonogramCopyright": "Wish Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Thunder Road", "labelName": "Rock Solid Records", "albumName": "Highway Dreams", "trackInfo": "A driving rock anthem about freedom and the open road", "primaryLanguage": "en", "upc": "123456780016", "isrc": "USRC25016", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.classic-rock", "copyrightName": "Rock Solid Records", "copyrightYear": "2024", "phonogramCopyright": "Rock Solid Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Rebel Heart", "labelName": "Underground Rock", "albumName": "Revolution", "trackInfo": "A fierce rock track about standing up for what you believe in", "primaryLanguage": "en", "upc": "123456780017", "isrc": "USRC25017", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Underground Rock", "copyrightYear": "2024", "phonogramCopyright": "Underground Rock", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Storm", "labelName": "Storm Records", "albumName": "Weather the Storm", "trackInfo": "An electrifying rock song with powerful guitar riffs and thunderous drums", "primaryLanguage": "en", "upc": "123456780018", "isrc": "USRC25018", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.hard-rock", "copyrightName": "Storm Records", "copyrightYear": "2024", "phonogramCopyright": "Storm Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Midnight Train", "labelName": "Journey Records", "albumName": "Night Travels", "trackInfo": "A nostalgic rock ballad about late-night journeys and self-discovery", "primaryLanguage": "en", "upc": "123456780019", "isrc": "USRC25019", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.indie-rock", "copyrightName": "Journey Records", "copyrightYear": "2024", "phonogramCopyright": "Journey Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Broken Wings", "labelName": "Emotional Rock", "albumName": "Healing Process", "trackInfo": "A powerful rock ballad about overcoming heartbreak and finding strength", "primaryLanguage": "en", "upc": "123456780020", "isrc": "USRC25020", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Emotional Rock", "copyrightYear": "2024", "phonogramCopyright": "Emotional Rock", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Rebellion", "labelName": "Punk Revival", "albumName": "City Lights", "trackInfo": "A punk-influenced rock anthem about youth rebellion in the modern world", "primaryLanguage": "en", "upc": "123456780021", "isrc": "USRC25021", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.punk", "copyrightName": "Punk Revival", "copyrightYear": "2024", "phonogramCopyright": "Punk Revival", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Gravity Falls", "labelName": "Indie Rock Collective", "albumName": "Falling Up", "trackInfo": "An indie rock exploration of feeling weightless in love", "primaryLanguage": "en", "upc": "123456780022", "isrc": "USRC25022", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.indie-rock", "copyrightName": "Indie Rock Collective", "copyrightYear": "2024", "phonogramCopyright": "Indie Rock Collective", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Fire and Steel", "labelName": "Metal Core Records", "albumName": "Forged in Fire", "trackInfo": "A heavy rock track with metal influences about inner strength and determination", "primaryLanguage": "en", "upc": "123456780023", "isrc": "USRC25023", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.metal", "copyrightName": "Metal Core Records", "copyrightYear": "2024", "phonogramCopyright": "Metal Core Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Sunset Boulevard", "labelName": "Classic Rock Revival", "albumName": "Golden Era", "trackInfo": "A classic rock homage to the golden age of rock and roll", "primaryLanguage": "en", "upc": "123456780024", "isrc": "USRC25024", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.classic-rock", "copyrightName": "Classic Rock Revival", "copyrightYear": "2024", "phonogramCopyright": "Classic Rock Revival", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Digital Dreams", "labelName": "Future Rock", "albumName": "Cyber Punk", "trackInfo": "A futuristic rock track blending traditional rock with electronic elements", "primaryLanguage": "en", "upc": "123456780025", "isrc": "USRC25025", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Future Rock", "copyrightYear": "2024", "phonogramCopyright": "Future Rock", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Grunge Revival", "labelName": "90s Nostalgia", "albumName": "Back to the 90s", "trackInfo": "A grunge-inspired rock track bringing back the raw energy of the 90s", "primaryLanguage": "en", "upc": "123456780026", "isrc": "USRC25026", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.grunge", "copyrightName": "90s Nostalgia", "copyrightYear": "2024", "phonogramCopyright": "90s Nostalgia", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Street Dreams", "labelName": "Urban Beats", "albumName": "City Life", "trackInfo": "A hip-hop anthem about pursuing dreams despite urban challenges", "primaryLanguage": "en", "upc": "123456780027", "isrc": "USRC25027", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Urban Beats", "copyrightYear": "2024", "phonogramCopyright": "Urban Beats", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Golden Chains", "labelName": "Luxury Rap", "albumName": "Success Story", "trackInfo": "A trap-influenced hip-hop track about success, wealth, and staying true to yourself", "primaryLanguage": "en", "upc": "123456780028", "isrc": "USRC25028", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Luxury Rap", "copyrightYear": "2024", "phonogramCopyright": "Luxury Rap", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "<PERSON> Hustle", "labelName": "Grind Records", "albumName": "Night Shift", "trackInfo": "A motivational hip-hop anthem about working hard and chasing dreams", "primaryLanguage": "en", "upc": "123456780029", "isrc": "USRC25029", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Grind Records", "copyrightYear": "2024", "phonogramCopyright": "Grind Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Nights", "labelName": "City Hip-Hop", "albumName": "Urban Glow", "trackInfo": "A smooth hip-hop track about nightlife and city adventures", "primaryLanguage": "en", "upc": "123456780030", "isrc": "USRC25030", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.chill", "copyrightName": "City Hip-Hop", "copyrightYear": "2024", "phonogramCopyright": "City Hip-Hop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Phoenix Flow", "labelName": "Rise Up Records", "albumName": "From Ashes", "trackInfo": "An inspirational hip-hop track about rising from failure to success", "primaryLanguage": "en", "upc": "123456780031", "isrc": "USRC25031", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.soul", "copyrightName": "Rise Up Records", "copyrightYear": "2024", "phonogramCopyright": "Rise Up Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Digital Crown", "labelName": "Tech Rap", "albumName": "Virtual Reality", "trackInfo": "A futuristic hip-hop track about success in the digital age", "primaryLanguage": "en", "upc": "123456780032", "isrc": "USRC25032", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Tech Rap", "copyrightYear": "2024", "phonogramCopyright": "Tech Rap", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Velvet Voice", "labelName": "Smooth Hip-Hop", "albumName": "Silk Roads", "trackInfo": "A smooth, R&B-influenced hip-hop track with silky vocals and mellow beats", "primaryLanguage": "en", "upc": "123456780033", "isrc": "USRC25033", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Smooth Hip-Hop", "copyrightYear": "2024", "phonogramCopyright": "Smooth Hip-Hop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Thunder Bass", "labelName": "Heavy Beats", "albumName": "Bass Drop", "trackInfo": "A hard-hitting hip-hop track with thunderous bass and aggressive lyrics", "primaryLanguage": "en", "upc": "123456780034", "isrc": "USRC25034", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Heavy Beats", "copyrightYear": "2024", "phonogramCopyright": "Heavy Beats", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Skyline Dreams", "labelName": "Urban Poetry", "albumName": "City Verses", "trackInfo": "A poetic hip-hop track about ambition and dreams in the big city", "primaryLanguage": "en", "upc": "123456780035", "isrc": "USRC25035", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Urban Poetry", "copyrightYear": "2024", "phonogramCopyright": "Urban Poetry", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Concrete Jungle", "labelName": "Street Sounds", "albumName": "Urban Tales", "trackInfo": "A gritty hip-hop narrative about life in the urban jungle", "primaryLanguage": "en", "upc": "123456780036", "isrc": "USRC25036", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Street Sounds", "copyrightYear": "2024", "phonogramCopyright": "Street Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Moonlight Serenade", "labelName": "Chill Hip-Hop", "albumName": "Night Vibes", "trackInfo": "A laid-back hip-hop track perfect for late-night contemplation", "primaryLanguage": "en", "upc": "123456780037", "isrc": "USRC25037", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.chill", "copyrightName": "Chill Hip-Hop", "copyrightYear": "2024", "phonogramCopyright": "Chill Hip-Hop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Victory Lap", "labelName": "Champion Records", "albumName": "Winners Circle", "trackInfo": "A triumphant hip-hop anthem celebrating success and perseverance", "primaryLanguage": "en", "upc": "123456780038", "isrc": "USRC25038", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Champion Records", "copyrightYear": "2024", "phonogramCopyright": "Champion Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Pulse", "labelName": "Electronic Fusion", "albumName": "Digital Heartbeat", "trackInfo": "A pulsating electronic track that captures the rhythm of modern life", "primaryLanguage": "en", "upc": "123456780039", "isrc": "USRC25039", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.dance", "copyrightName": "Electronic Fusion", "copyrightYear": "2024", "phonogramCopyright": "Electronic Fusion", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Cyber Dreams", "labelName": "Future Sounds", "albumName": "Virtual Reality", "trackInfo": "A futuristic electronic journey through digital landscapes", "primaryLanguage": "en", "upc": "123456780040", "isrc": "USRC25040", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.ambient", "copyrightName": "Future Sounds", "copyrightYear": "2024", "phonogramCopyright": "Future Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Nights", "labelName": "Club Beats", "albumName": "Dance Floor", "trackInfo": "An energetic electronic dance track designed to light up the dance floor", "primaryLanguage": "en", "upc": "123456780041", "isrc": "USRC25041", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.edm", "copyrightName": "Club Beats", "copyrightYear": "2024", "phonogramCopyright": "Club Beats", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Synthetic Love", "labelName": "Digital Romance", "albumName": "Artificial Hearts", "trackInfo": "A synth-heavy electronic track exploring love in the digital age", "primaryLanguage": "en", "upc": "123456780042", "isrc": "USRC25042", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.synth-pop", "copyrightName": "Digital Romance", "copyrightYear": "2024", "phonogramCopyright": "Digital Romance", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Bass Drop Paradise", "labelName": "Heavy Electronic", "albumName": "Bass Heaven", "trackInfo": "A bass-heavy electronic track with massive drops and intense energy", "primaryLanguage": "en", "upc": "123456780043", "isrc": "USRC25043", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.dubstep", "copyrightName": "Heavy Electronic", "copyrightYear": "2024", "phonogramCopyright": "Heavy Electronic", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Midnight Frequency", "labelName": "Deep House Records", "albumName": "Underground Vibes", "trackInfo": "A deep house track with hypnotic rhythms and atmospheric sounds", "primaryLanguage": "en", "upc": "123456780044", "isrc": "USRC25044", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.house", "copyrightName": "Deep House Records", "copyrightYear": "2024", "phonogramCopyright": "Deep House Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Digital Horizon", "labelName": "Ambient Electronic", "albumName": "Infinite Space", "trackInfo": "An ambient electronic piece that creates vast digital soundscapes", "primaryLanguage": "en", "upc": "123456780045", "isrc": "USRC25045", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.ambient", "copyrightName": "Ambient Electronic", "copyrightYear": "2024", "phonogramCopyright": "Ambient Electronic", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Techno Revolution", "labelName": "Underground Techno", "albumName": "Machine Dreams", "trackInfo": "A driving techno track with industrial influences and relentless energy", "primaryLanguage": "en", "upc": "123456780046", "isrc": "USRC25046", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.techno", "copyrightName": "Underground Techno", "copyrightYear": "2024", "phonogramCopyright": "Underground Techno", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Trance State", "labelName": "Euphoric Sounds", "albumName": "Higher Consciousness", "trackInfo": "An uplifting trance track that takes listeners on a euphoric journey", "primaryLanguage": "en", "upc": "123456780047", "isrc": "USRC25047", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.trance", "copyrightName": "Euphoric Sounds", "copyrightYear": "2024", "phonogramCopyright": "Euphoric Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Drum Machine Dreams", "labelName": "Rhythm Electronic", "albumName": "<PERSON>", "trackInfo": "A drum and bass influenced electronic track with complex rhythmic patterns", "primaryLanguage": "en", "upc": "123456780048", "isrc": "USRC25048", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.drum-and-bass", "copyrightName": "Rhythm Electronic", "copyrightYear": "2024", "phonogramCopyright": "Rhythm Electronic", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Velvet Nights", "labelName": "Smooth R&B", "albumName": "Silk and Soul", "trackInfo": "A smooth R&B ballad with silky vocals and romantic undertones", "primaryLanguage": "en", "upc": "123456780049", "isrc": "USRC25049", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.soul", "copyrightName": "Smooth R&B", "copyrightYear": "2024", "phonogramCopyright": "Smooth R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Midnight Confessions", "labelName": "Intimate R&B", "albumName": "Late Night Talks", "trackInfo": "An intimate R&B track about late-night conversations and deep connections", "primaryLanguage": "en", "upc": "123456780050", "isrc": "USRC25050", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.pop", "copyrightName": "Intimate R&B", "copyrightYear": "2024", "phonogramCopyright": "Intimate R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Golden Hour Love", "labelName": "Neo Soul Records", "albumName": "Modern Soul", "trackInfo": "A neo-soul influenced R&B track celebrating perfect moments in love", "primaryLanguage": "en", "upc": "123456780051", "isrc": "USRC25051", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.soul", "copyrightName": "Neo Soul Records", "copyrightYear": "2024", "phonogramCopyright": "Neo Soul Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Smooth Operator", "labelName": "Classic R&B", "albumName": "Timeless Groove", "trackInfo": "A classic R&B track with funky basslines and smooth vocal delivery", "primaryLanguage": "en", "upc": "123456780052", "isrc": "USRC25052", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.funk", "copyrightName": "Classic R&B", "copyrightYear": "2024", "phonogramCopyright": "Classic R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Touch", "labelName": "Modern R&B", "albumName": "Digital Soul", "trackInfo": "A contemporary R&B track blending traditional soul with modern production", "primaryLanguage": "en", "upc": "123456780053", "isrc": "USRC25053", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Modern R&B", "copyrightYear": "2024", "phonogramCopyright": "Modern R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soulful <PERSON>", "labelName": "Deep Soul", "albumName": "Heart and Soul", "trackInfo": "A deeply emotional R&B ballad showcasing powerful vocals and heartfelt lyrics", "primaryLanguage": "en", "upc": "123456780054", "isrc": "USRC25054", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.soul", "copyrightName": "Deep Soul", "copyrightYear": "2024", "phonogramCopyright": "Deep Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Midnight Blues", "labelName": "R&B Classics", "albumName": "Blue Notes", "trackInfo": "A blues-influenced R&B track with emotional depth and soulful guitar", "primaryLanguage": "en", "upc": "123456780055", "isrc": "USRC25055", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.blues", "copyrightName": "R&B Classics", "copyrightYear": "2024", "phonogramCopyright": "R&B Classics", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Groove Machine", "labelName": "Funk R&B", "albumName": "Get Down", "trackInfo": "A funky R&B track with infectious grooves and danceable rhythms", "primaryLanguage": "en", "upc": "123456780056", "isrc": "USRC25056", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.funk", "copyrightName": "Funk R&B", "copyrightYear": "2024", "phonogramCopyright": "Funk R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Sunset Dreams", "labelName": "Chill R&B", "albumName": "Evening Moods", "trackInfo": "A laid-back R&B track perfect for sunset moments and relaxation", "primaryLanguage": "en", "upc": "123456780057", "isrc": "USRC25057", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.chill", "copyrightName": "Chill R&B", "copyrightYear": "2024", "phonogramCopyright": "Chill R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Love Language", "labelName": "Contemporary R&B", "albumName": "Modern Romance", "trackInfo": "A contemporary R&B track about expressing love in different ways", "primaryLanguage": "en", "upc": "123456780058", "isrc": "USRC25058", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.pop", "copyrightName": "Contemporary R&B", "copyrightYear": "2024", "phonogramCopyright": "Contemporary R&B", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Coffee Shop Dreams", "labelName": "Indie Folk Collective", "albumName": "Acoustic Mornings", "trackInfo": "A cozy indie folk song about finding inspiration in everyday moments", "primaryLanguage": "en", "upc": "123456780059", "isrc": "USRC25059", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.folk", "copyrightName": "Indie Folk Collective", "copyrightYear": "2024", "phonogramCopyright": "Indie Folk Collective", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neon Indie", "labelName": "Urban Indie", "albumName": "City Lights", "trackInfo": "An indie rock track with electronic influences and urban themes", "primaryLanguage": "en", "upc": "123456780060", "isrc": "USRC25060", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Urban Indie", "copyrightYear": "2024", "phonogramCopyright": "Urban Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Vintage Vibes", "labelName": "Retro Indie", "albumName": "Nostalgic Sounds", "trackInfo": "An indie pop track with vintage influences and nostalgic melodies", "primaryLanguage": "en", "upc": "123456780061", "isrc": "USRC25061", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.pop", "copyrightName": "Retro Indie", "copyrightYear": "2024", "phonogramCopyright": "Retro Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Moonlit Forest", "labelName": "Nature Sounds", "albumName": "Wilderness", "trackInfo": "A dreamy indie folk song inspired by nature and moonlit nights", "primaryLanguage": "en", "upc": "123456780062", "isrc": "USRC25062", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.folk", "copyrightName": "Nature Sounds", "copyrightYear": "2024", "phonogramCopyright": "Nature Sounds", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Bedroom Pop", "labelName": "Lo-Fi Indie", "albumName": "Home Recordings", "trackInfo": "A lo-fi indie track with intimate vocals and bedroom production aesthetics", "primaryLanguage": "en", "upc": "123456780063", "isrc": "USRC25063", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.lo-fi", "copyrightName": "Lo-Fi Indie", "copyrightYear": "2024", "phonogramCopyright": "Lo-Fi Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alternative Reality", "labelName": "Alt Indie", "albumName": "Different Worlds", "trackInfo": "An alternative indie track exploring themes of escapism and imagination", "primaryLanguage": "en", "upc": "123456780064", "isrc": "USRC25064", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Alt Indie", "copyrightYear": "2024", "phonogramCopyright": "Alt Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Synth Dreams", "labelName": "Synth Indie", "albumName": "Electric Indie", "trackInfo": "An indie track with prominent synthesizers and dreamy atmospheres", "primaryLanguage": "en", "upc": "123456780065", "isrc": "USRC25065", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.synth-pop", "copyrightName": "Synth Indie", "copyrightYear": "2024", "phonogramCopyright": "Synth Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Indie Rock Anthem", "labelName": "Rock Indie", "albumName": "Guitar Heroes", "trackInfo": "An energetic indie rock anthem with powerful guitar riffs and driving rhythms", "primaryLanguage": "en", "upc": "123456780066", "isrc": "USRC25066", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.rock", "copyrightName": "Rock Indie", "copyrightYear": "2024", "phonogramCopyright": "Rock Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Dreamy Indie", "labelName": "Dream Indie", "albumName": "Floating Sounds", "trackInfo": "A dreamy indie track with ethereal vocals and atmospheric production", "primaryLanguage": "en", "upc": "123456780067", "isrc": "USRC25067", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.ambient", "copyrightName": "Dream Indie", "copyrightYear": "2024", "phonogramCopyright": "Dream Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Indie Dance Floor", "labelName": "Dance Indie", "albumName": "Move to This", "trackInfo": "An upbeat indie track that makes you want to dance with infectious rhythms", "primaryLanguage": "en", "upc": "123456780068", "isrc": "USRC25068", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.dance", "copyrightName": "Dance Indie", "copyrightYear": "2024", "phonogramCopyright": "Dance Indie", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Campfire Stories", "labelName": "Folk Tales Records", "albumName": "Around the Fire", "trackInfo": "A traditional folk song telling stories of old times and simple life", "primaryLanguage": "en", "upc": "123456780069", "isrc": "USRC25069", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.country", "copyrightName": "Folk Tales Records", "copyrightYear": "2024", "phonogramCopyright": "Folk Tales Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Mountain Song", "labelName": "Acoustic Folk", "albumName": "High Country", "trackInfo": "An acoustic folk ballad inspired by mountain landscapes and natural beauty", "primaryLanguage": "en", "upc": "123456780070", "isrc": "USRC25070", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.singer-songwriter", "copyrightName": "Acoustic Folk", "copyrightYear": "2024", "phonogramCopyright": "Acoustic Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "River's Edge", "labelName": "Nature Folk", "albumName": "Flowing Waters", "trackInfo": "A peaceful folk song about finding solace by the river's edge", "primaryLanguage": "en", "upc": "123456780071", "isrc": "USRC25071", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.indie", "copyrightName": "Nature Folk", "copyrightYear": "2024", "phonogramCopyright": "Nature Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "<PERSON><PERSON><PERSON>'s Tale", "labelName": "Journey Folk", "albumName": "Roads Less Traveled", "trackInfo": "A folk narrative about a wanderer's journey through life and landscapes", "primaryLanguage": "en", "upc": "123456780072", "isrc": "USRC25072", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.singer-songwriter", "copyrightName": "Journey Folk", "copyrightYear": "2024", "phonogramCopyright": "Journey Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Autumn Leaves", "labelName": "Seasonal Folk", "albumName": "Four Seasons", "trackInfo": "A melancholic folk song about change and the passage of time", "primaryLanguage": "en", "upc": "123456780073", "isrc": "USRC25073", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.indie", "copyrightName": "Seasonal Folk", "copyrightYear": "2024", "phonogramCopyright": "Seasonal Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Folk", "labelName": "Modern Folk", "albumName": "Folk Evolution", "trackInfo": "A contemporary folk track blending traditional elements with modern production", "primaryLanguage": "en", "upc": "123456780074", "isrc": "USRC25074", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Modern Folk", "copyrightYear": "2024", "phonogramCopyright": "Modern Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Folk Rock Anthem", "labelName": "Folk Rock Records", "albumName": "Electric Acoustic", "trackInfo": "A powerful folk rock anthem with electric guitars and traditional folk melodies", "primaryLanguage": "en", "upc": "123456780075", "isrc": "USRC25075", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.rock", "copyrightName": "Folk Rock Records", "copyrightYear": "2024", "phonogramCopyright": "Folk Rock Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Storyteller", "labelName": "Narrative Folk", "albumName": "Tales and Legends", "trackInfo": "A folk song that tells a captivating story through beautiful melodies", "primaryLanguage": "en", "upc": "123456780076", "isrc": "USRC25076", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.singer-songwriter", "copyrightName": "Narrative Folk", "copyrightYear": "2024", "phonogramCopyright": "Narrative Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Folk Pop Fusion", "labelName": "Crossover Folk", "albumName": "Folk Meets Pop", "trackInfo": "A catchy folk-pop fusion that bridges traditional and contemporary sounds", "primaryLanguage": "en", "upc": "123456780077", "isrc": "USRC25077", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.pop", "copyrightName": "Crossover Folk", "copyrightYear": "2024", "phonogramCopyright": "Crossover Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Bluegrass Morning", "labelName": "Traditional Folk", "albumName": "Mountain Music", "trackInfo": "A traditional bluegrass-influenced folk song celebrating rural life", "primaryLanguage": "en", "upc": "123456780078", "isrc": "USRC25078", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.bluegrass", "copyrightName": "Traditional Folk", "copyrightYear": "2024", "phonogramCopyright": "Traditional Folk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Country Road Home", "labelName": "Nashville Records", "albumName": "Hometown Stories", "trackInfo": "A heartfelt country ballad about returning to your roots and hometown", "primaryLanguage": "en", "upc": "123456780079", "isrc": "USRC25079", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.folk", "copyrightName": "Nashville Records", "copyrightYear": "2024", "phonogramCopyright": "Nashville Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Whiskey Nights", "labelName": "Honky Tonk Records", "albumName": "Bar Room Tales", "trackInfo": "A classic country song about drowning sorrows and late-night reflections", "primaryLanguage": "en", "upc": "123456780080", "isrc": "USRC25080", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.blues", "copyrightName": "Honky Tonk Records", "copyrightYear": "2024", "phonogramCopyright": "Honky Tonk Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Truck Stop Blues", "labelName": "Highway Country", "albumName": "Road Songs", "trackInfo": "A country rock song about life on the road and truck stop encounters", "primaryLanguage": "en", "upc": "123456780081", "isrc": "USRC25081", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.rock", "copyrightName": "Highway Country", "copyrightYear": "2024", "phonogramCopyright": "Highway Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Small Town Dreams", "labelName": "Rural Records", "albumName": "Simple Life", "trackInfo": "An uplifting country pop song about big dreams in a small town", "primaryLanguage": "en", "upc": "123456780082", "isrc": "USRC25082", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.pop", "copyrightName": "Rural Records", "copyrightYear": "2024", "phonogramCopyright": "Rural Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Barn Dance", "labelName": "Traditional Country", "albumName": "Country Traditions", "trackInfo": "A lively country song perfect for dancing and celebrating rural traditions", "primaryLanguage": "en", "upc": "123456780083", "isrc": "USRC25083", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.bluegrass", "copyrightName": "Traditional Country", "copyrightYear": "2024", "phonogramCopyright": "Traditional Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Sunset on the Ranch", "labelName": "Western Country", "albumName": "Wide Open Spaces", "trackInfo": "A peaceful country ballad about the beauty of ranch life and wide open spaces", "primaryLanguage": "en", "upc": "123456780084", "isrc": "USRC25084", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.folk", "copyrightName": "Western Country", "copyrightYear": "2024", "phonogramCopyright": "Western Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Country Pop Crossover", "labelName": "Crossover Country", "albumName": "Radio Ready", "trackInfo": "A modern country-pop crossover hit with mainstream appeal", "primaryLanguage": "en", "upc": "123456780085", "isrc": "USRC25085", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.pop", "copyrightName": "Crossover Country", "copyrightYear": "2024", "phonogramCopyright": "Crossover Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Backwoods Anthem", "labelName": "Rural Roots", "albumName": "Country Pride", "trackInfo": "A proud country anthem celebrating rural life and values", "primaryLanguage": "en", "upc": "123456780086", "isrc": "USRC25086", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.rock", "copyrightName": "Rural Roots", "copyrightYear": "2024", "phonogramCopyright": "Rural Roots", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Country Love Song", "labelName": "Heart Country", "albumName": "Love and Life", "trackInfo": "A tender country love song about finding your soulmate", "primaryLanguage": "en", "upc": "123456780087", "isrc": "USRC25087", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.folk", "copyrightName": "Heart Country", "copyrightYear": "2024", "phonogramCopyright": "Heart Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Highway Memories", "labelName": "Road Country", "albumName": "Miles and Memories", "trackInfo": "A nostalgic country song about memories made on long highway drives", "primaryLanguage": "en", "upc": "123456780088", "isrc": "USRC25088", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.blues", "copyrightName": "Road Country", "copyrightYear": "2024", "phonogramCopyright": "Road Country", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Smooth Jazz Nights", "labelName": "Jazz Lounge Records", "albumName": "After Hours", "trackInfo": "A smooth jazz instrumental perfect for late-night relaxation", "primaryLanguage": "en", "upc": "123456780089", "isrc": "USRC25089", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.blues", "copyrightName": "Jazz Lounge Records", "copyrightYear": "2024", "phonogramCopyright": "Jazz Lounge Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Bebop Dreams", "labelName": "Classic Jazz", "albumName": "Jazz Standards", "trackInfo": "A bebop-influenced jazz piece with complex harmonies and improvisation", "primaryLanguage": "en", "upc": "123456780090", "isrc": "USRC25090", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.funk", "copyrightName": "Classic Jazz", "copyrightYear": "2024", "phonogramCopyright": "Classic Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Jazz Fusion", "labelName": "Modern Jazz", "albumName": "Electric Jazz", "trackInfo": "A jazz fusion track blending traditional jazz with electronic elements", "primaryLanguage": "en", "upc": "123456780091", "isrc": "USRC25091", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Modern Jazz", "copyrightYear": "2024", "phonogramCopyright": "Modern Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Midnight Saxophone", "labelName": "Sax Jazz", "albumName": "Saxophone Serenades", "trackInfo": "A soulful jazz piece featuring beautiful saxophone melodies", "primaryLanguage": "en", "upc": "123456780092", "isrc": "USRC25092", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.soul", "copyrightName": "Sax Jazz", "copyrightYear": "2024", "phonogramCopyright": "Sax Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Jazz Cafe", "labelName": "Cafe Jazz", "albumName": "Coffee House Sessions", "trackInfo": "A relaxing jazz track perfect for coffee shop ambiance", "primaryLanguage": "en", "upc": "123456780093", "isrc": "USRC25093", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.ambient", "copyrightName": "Cafe Jazz", "copyrightYear": "2024", "phonogramCopyright": "Cafe Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Swing Time", "labelName": "Big Band Jazz", "albumName": "Dance Hall Days", "trackInfo": "An upbeat swing jazz number that makes you want to dance", "primaryLanguage": "en", "upc": "123456780094", "isrc": "USRC25094", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.dance", "copyrightName": "Big Band Jazz", "copyrightYear": "2024", "phonogramCopyright": "Big Band Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Cool Jazz", "labelName": "Cool Cat Records", "albumName": "Chill Jazz", "trackInfo": "A cool jazz piece with laid-back rhythms and sophisticated harmonies", "primaryLanguage": "en", "upc": "123456780095", "isrc": "USRC25095", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.chill", "copyrightName": "Cool Cat Records", "copyrightYear": "2024", "phonogramCopyright": "Cool Cat Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Jazz Ballad", "labelName": "Romantic Jazz", "albumName": "Love Songs in Jazz", "trackInfo": "A romantic jazz ballad with tender melodies and heartfelt expression", "primaryLanguage": "en", "upc": "123456780096", "isrc": "USRC25096", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.soul", "copyrightName": "Romantic Jazz", "copyrightYear": "2024", "phonogramCopyright": "Romantic Jazz", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alternative Reality", "labelName": "Alt Rock Records", "albumName": "Different Worlds", "trackInfo": "An alternative rock anthem about escaping reality and finding your own path", "primaryLanguage": "en", "upc": "123456780097", "isrc": "USRC25097", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.rock", "copyrightName": "Alt Rock Records", "copyrightYear": "2024", "phonogramCopyright": "Alt Rock Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Underground Sound", "labelName": "Alternative Music", "albumName": "Below the Surface", "trackInfo": "An underground alternative track with experimental sounds and raw energy", "primaryLanguage": "en", "upc": "123456780098", "isrc": "USRC25098", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.indie", "copyrightName": "Alternative Music", "copyrightYear": "2024", "phonogramCopyright": "Alternative Music", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alt Pop Fusion", "labelName": "Crossover Alt", "albumName": "Pop Alternative", "trackInfo": "A catchy alternative pop fusion with mainstream appeal and indie credibility", "primaryLanguage": "en", "upc": "123456780099", "isrc": "USRC25099", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.pop", "copyrightName": "Crossover Alt", "copyrightYear": "2024", "phonogramCopyright": "Crossover Alt", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electronic Alternative", "labelName": "Digital Alt", "albumName": "Cyber Alternative", "trackInfo": "An electronic-influenced alternative track blending digital and organic sounds", "primaryLanguage": "en", "upc": "123456780100", "isrc": "USRC25100", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Digital Alt", "copyrightYear": "2024", "phonogramCopyright": "Digital Alt", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alternative Ballad", "labelName": "Emotional Alt", "albumName": "Deep Feelings", "trackInfo": "A deeply emotional alternative ballad with introspective lyrics", "primaryLanguage": "en", "upc": "123456780101", "isrc": "USRC25101", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.indie", "copyrightName": "Emotional Alt", "copyrightYear": "2024", "phonogramCopyright": "Emotional Alt", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Grunge Revival", "labelName": "90s Alternative", "albumName": "Grunge Returns", "trackInfo": "A grunge-influenced alternative track bringing back the raw 90s sound", "primaryLanguage": "en", "upc": "123456780102", "isrc": "USRC25102", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.grunge", "copyrightName": "90s Alternative", "copyrightYear": "2024", "phonogramCopyright": "90s Alternative", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alternative Punk", "labelName": "Punk <PERSON>", "albumName": "Rebel Alternative", "trackInfo": "A punk-influenced alternative track with rebellious energy and attitude", "primaryLanguage": "en", "upc": "123456780103", "isrc": "USRC25103", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.punk", "copyrightName": "Punk <PERSON>", "copyrightYear": "2024", "phonogramCopyright": "Punk <PERSON>", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Alternative Metal", "labelName": "Heavy Alt", "albumName": "Metal Alternative", "trackInfo": "A heavy alternative track with metal influences and powerful riffs", "primaryLanguage": "en", "upc": "123456780104", "isrc": "USRC25104", "primaryGenreId": "music.genre.alternative", "secondaryGenreId": "music.genre.metal", "copyrightName": "Heavy Alt", "copyrightYear": "2024", "phonogramCopyright": "Heavy Alt", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Funk Machine", "labelName": "Groove Records", "albumName": "Get Funky", "trackInfo": "A classic funk track with infectious grooves and tight rhythms", "primaryLanguage": "en", "upc": "123456780105", "isrc": "USRC25105", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.soul", "copyrightName": "Groove Records", "copyrightYear": "2024", "phonogramCopyright": "Groove Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Disco Funk", "labelName": "Disco Groove", "albumName": "Dance Floor Funk", "trackInfo": "A disco-influenced funk track perfect for dancing and grooving", "primaryLanguage": "en", "upc": "123456780106", "isrc": "USRC25106", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.disco", "copyrightName": "Disco Groove", "copyrightYear": "2024", "phonogramCopyright": "Disco Groove", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Modern Funk", "labelName": "Contemporary Funk", "albumName": "Funk Evolution", "trackInfo": "A modern funk track blending classic grooves with contemporary production", "primaryLanguage": "en", "upc": "123456780107", "isrc": "USRC25107", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Contemporary Funk", "copyrightYear": "2024", "phonogramCopyright": "Contemporary Funk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Funk Rock", "labelName": "<PERSON>", "albumName": "Funky Rock", "trackInfo": "A funk rock fusion with powerful guitar riffs and funky basslines", "primaryLanguage": "en", "upc": "123456780108", "isrc": "USRC25108", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.rock", "copyrightName": "<PERSON>", "copyrightYear": "2024", "phonogramCopyright": "<PERSON>", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Funk Jazz", "labelName": "Jazz Funk", "albumName": "Fusion Grooves", "trackInfo": "A jazz-funk fusion with sophisticated harmonies and funky rhythms", "primaryLanguage": "en", "upc": "123456780109", "isrc": "USRC25109", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.jazz", "copyrightName": "Jazz Funk", "copyrightYear": "2024", "phonogramCopyright": "Jazz Funk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Funk Pop", "labelName": "Pop Funk", "albumName": "Funky Pop", "trackInfo": "A catchy funk-pop track with mainstream appeal and infectious grooves", "primaryLanguage": "en", "upc": "123456780110", "isrc": "USRC25110", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.pop", "copyrightName": "Pop Funk", "copyrightYear": "2024", "phonogramCopyright": "Pop Funk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Hip-Hop Funk", "labelName": "Funk Hip-Hop", "albumName": "Funky Beats", "trackInfo": "A hip-hop funk fusion with rap vocals over funky instrumental tracks", "primaryLanguage": "en", "upc": "123456780111", "isrc": "USRC25111", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.hip-hop", "copyrightName": "Funk Hip-Hop", "copyrightYear": "2024", "phonogramCopyright": "Funk Hip-Hop", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "<PERSON> Bass", "labelName": "<PERSON> Funk", "albumName": "Low End Theory", "trackInfo": "A bass-heavy funk track showcasing incredible bassline grooves", "primaryLanguage": "en", "upc": "123456780112", "isrc": "USRC25112", "primaryGenreId": "music.genre.funk", "secondaryGenreId": "music.genre.rnb", "copyrightName": "<PERSON> Funk", "copyrightYear": "2024", "phonogramCopyright": "<PERSON> Funk", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Power", "labelName": "Deep Soul Records", "albumName": "Soul Revival", "trackInfo": "A powerful soul anthem with emotional vocals and gospel influences", "primaryLanguage": "en", "upc": "123456780113", "isrc": "USRC25113", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.gospel", "copyrightName": "Deep Soul Records", "copyrightYear": "2024", "phonogramCopyright": "Deep Soul Records", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Neo Soul", "labelName": "Modern Soul", "albumName": "Soul Evolution", "trackInfo": "A contemporary neo-soul track blending classic soul with modern R&B", "primaryLanguage": "en", "upc": "123456780114", "isrc": "USRC25114", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Modern Soul", "copyrightYear": "2024", "phonogramCopyright": "Modern Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Ballad", "labelName": "Emotional Soul", "albumName": "Heart and Soul", "trackInfo": "A deeply emotional soul ballad with powerful vocals and heartfelt lyrics", "primaryLanguage": "en", "upc": "123456780115", "isrc": "USRC25115", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.blues", "copyrightName": "Emotional Soul", "copyrightYear": "2024", "phonogramCopyright": "Emotional Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Funk", "labelName": "Funky Soul", "albumName": "Groove and Soul", "trackInfo": "A soul-funk fusion with infectious grooves and soulful vocals", "primaryLanguage": "en", "upc": "123456780116", "isrc": "USRC25116", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.funk", "copyrightName": "Funky Soul", "copyrightYear": "2024", "phonogramCopyright": "Funky Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Jazz", "labelName": "Jazz Soul", "albumName": "Soulful Jazz", "trackInfo": "A soul-jazz fusion with sophisticated harmonies and emotional depth", "primaryLanguage": "en", "upc": "123456780117", "isrc": "USRC25117", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.jazz", "copyrightName": "Jazz Soul", "copyrightYear": "2024", "phonogramCopyright": "Jazz Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Pop", "labelName": "Pop Soul", "albumName": "Soulful Pop", "trackInfo": "A soul-pop crossover with mainstream appeal and soulful vocals", "primaryLanguage": "en", "upc": "123456780118", "isrc": "USRC25118", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.pop", "copyrightName": "Pop Soul", "copyrightYear": "2024", "phonogramCopyright": "Pop Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Rock", "labelName": "Rock Soul", "albumName": "Soulful Rock", "trackInfo": "A soul-rock fusion with powerful guitar riffs and soulful vocals", "primaryLanguage": "en", "upc": "123456780119", "isrc": "USRC25119", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.rock", "copyrightName": "Rock Soul", "copyrightYear": "2024", "phonogramCopyright": "Rock Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Soul Gospel", "labelName": "Gospel Soul", "albumName": "Spiritual Soul", "trackInfo": "A gospel-influenced soul track with spiritual themes and powerful vocals", "primaryLanguage": "en", "upc": "123456780120", "isrc": "USRC25120", "primaryGenreId": "music.genre.soul", "secondaryGenreId": "music.genre.gospel", "copyrightName": "Gospel Soul", "copyrightYear": "2024", "phonogramCopyright": "Gospel Soul", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Electric Blues", "labelName": "Blues Electric", "albumName": "Electric Blues Revival", "trackInfo": "A powerful electric blues track with searing guitar solos and emotional vocals", "primaryLanguage": "en", "upc": "123456780121", "isrc": "USRC25121", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.rock", "copyrightName": "Blues Electric", "copyrightYear": "2024", "phonogramCopyright": "Blues Electric", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Delta Blues", "labelName": "Traditional Blues", "albumName": "Delta Roots", "trackInfo": "A traditional delta blues song with acoustic guitar and raw vocals", "primaryLanguage": "en", "upc": "123456780122", "isrc": "USRC25122", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.folk", "copyrightName": "Traditional Blues", "copyrightYear": "2024", "phonogramCopyright": "Traditional Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Chicago Blues", "labelName": "Urban Blues", "albumName": "City Blues", "trackInfo": "A Chicago-style blues with harmonica and electric guitar", "primaryLanguage": "en", "upc": "123456780123", "isrc": "USRC25123", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.jazz", "copyrightName": "Urban Blues", "copyrightYear": "2024", "phonogramCopyright": "Urban Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Blues Rock", "labelName": "Rock Blues", "albumName": "Blues Rock Fusion", "trackInfo": "A blues rock fusion with powerful guitar riffs and blues progressions", "primaryLanguage": "en", "upc": "123456780124", "isrc": "USRC25124", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.rock", "copyrightName": "Rock Blues", "copyrightYear": "2024", "phonogramCopyright": "Rock Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Blues Soul", "labelName": "Soulful Blues", "albumName": "Soul Blues", "trackInfo": "A soul-influenced blues track with emotional vocals and gospel touches", "primaryLanguage": "en", "upc": "123456780125", "isrc": "USRC25125", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.soul", "copyrightName": "Soulful Blues", "copyrightYear": "2024", "phonogramCopyright": "Soulful Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Modern Blues", "labelName": "Contemporary Blues", "albumName": "Blues Today", "trackInfo": "A contemporary blues track blending traditional blues with modern production", "primaryLanguage": "en", "upc": "123456780126", "isrc": "USRC25126", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Contemporary Blues", "copyrightYear": "2024", "phonogramCopyright": "Contemporary Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Blues Funk", "labelName": "Funky Blues", "albumName": "Groove Blues", "trackInfo": "A funk-influenced blues track with groovy basslines and blues guitar", "primaryLanguage": "en", "upc": "123456780127", "isrc": "USRC25127", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.funk", "copyrightName": "Funky Blues", "copyrightYear": "2024", "phonogramCopyright": "Funky Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Country Blues", "labelName": "Rural Blues", "albumName": "Country Blues", "trackInfo": "A country-influenced blues track with acoustic guitar and storytelling lyrics", "primaryLanguage": "en", "upc": "123456780128", "isrc": "USRC25128", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.country", "copyrightName": "Rural Blues", "copyrightYear": "2024", "phonogramCopyright": "Rural Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Blues Jazz", "labelName": "Jazz Blues", "albumName": "Jazz Blues Fusion", "trackInfo": "A jazz-blues fusion with sophisticated harmonies and improvisation", "primaryLanguage": "en", "upc": "123456780129", "isrc": "USRC25129", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.jazz", "copyrightName": "Jazz Blues", "copyrightYear": "2024", "phonogramCopyright": "Jazz Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}, {"title": "Acoustic Blues", "labelName": "Pure Blues", "albumName": "Unplugged Blues", "trackInfo": "A pure acoustic blues track with fingerpicked guitar and heartfelt vocals", "primaryLanguage": "en", "upc": "123456780130", "isrc": "USRC25130", "primaryGenreId": "music.genre.blues", "secondaryGenreId": "music.genre.folk", "copyrightName": "Pure Blues", "copyrightYear": "2024", "phonogramCopyright": "Pure Blues", "phonogramCopyrightYear": "2024", "audioFormats": ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"], "releaseOptions": ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording"], "medias": [{"mediaUrl": "https://cdn.example.com/audio/shining-lights.mp3", "type": 1}, {"mediaUrl": "https://cdn.example.com/audio/shining-lights.flac", "type": 3}]}]}